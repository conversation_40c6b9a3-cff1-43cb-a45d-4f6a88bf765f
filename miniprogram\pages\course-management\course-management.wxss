/**
 * course-management.wxss - 活动管理页面样式文件
 * 
 * 优化说明：
 * - 统一使用rpx单位，确保响应式布局
 * - 移除重复的选择器和属性声明
 * - 按功能模块组织样式规则
 * - 优化性能和可维护性
 */

/* ==================== 基础布局样式 ==================== */

page, .page {
  height: 100%;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background-color: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  overflow-x: hidden;
}

/* ==================== 顶部区域样式 ==================== */

.top-section {
  flex-shrink: 0;
  width: 100%;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 100;
}

.top-tabs-section {
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
  padding: 0 32rpx 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.top-tabs-section:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

.custom-top-tabs {
  background-color: transparent;
  border: none;
  font-size: 32rpx;
  margin: 0;
  width: 100%;
  height: auto;
}

.custom-top-tabs .t-tabs__nav {
  padding: 0;
  height: auto;
  border-bottom: none;
  display: flex;
  align-items: center;
  background: transparent;
}

.custom-top-tabs .t-tabs__item {
  font-size: 32rpx !important;
  font-weight: 500;
  padding: 28rpx 40rpx !important;
  height: auto;
  line-height: 1.4;
  min-height: 88rpx;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0;
  background: transparent !important;
  border-bottom: 6rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #666666 !important;
  position: relative;
}

.custom-top-tabs .t-tabs__item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  width: 0;
  height: 4rpx;
  background: linear-gradient(90deg, transparent, #0052d9, transparent);
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-top-tabs .t-tabs__item--active {
  color: #0052d9 !important;
  font-weight: 600 !important;
  border-bottom-color: #0052d9 !important;
  background: transparent !important;
  text-shadow: 0 0 2rpx rgba(0, 82, 217, 0.1);
}

.custom-top-tabs .t-tabs__item--active::before {
  width: 60%;
}

.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover {
  color: #333333 !important;
  background: transparent !important;
  border-bottom-color: rgba(0, 82, 217, 0.3) !important;
}

.custom-top-tabs .t-tabs__item:not(.t-tabs__item--active):hover::before {
  width: 30%;
}

.custom-top-tabs .t-tabs__track {
  display: none;
}

/* ==================== 筛选区域样式 ==================== */

.filter-section {
  width: 100%;
  background: transparent;
  border: none;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  transition: all 0.2s ease;
  overflow: visible;
  z-index: 90;
  position: relative;
  margin-bottom: 32rpx;
}

/* ==================== 活动列表样式 ==================== */

.course-list, .template-list {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
}

.course-content, .template-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  box-sizing: border-box;
  -webkit-overflow-scrolling: touch;
}

.timeline-date {
  margin: 16rpx;
  font-size: 28rpx;
  color: #0052d9;
  font-weight: bold;
  position: relative;
  padding-left: 32rpx;
}

.timeline-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 16rpx;
  height: 16rpx;
  background: #0052d9;
  border-radius: 50%;
  transform: translateY(-50%);
}

/* ==================== 活动/模板卡片样式 ==================== */

.course-card, .template-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  transition: all 0.2s ease;
}

.course-card:active, .template-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

.course-card.slide-in {
  animation: slide-in-up 0.6s ease-out;
}

@keyframes slide-in-up {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.course-header, .template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  gap: 16rpx;
}

.course-title, .template-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 状态标签样式 ==================== */

.course-status {
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  flex-shrink: 0;
  white-space: nowrap;
}

/* 状态标签颜色 */
.course-status.available { background-color: #e8f5e8; color: #52c41a; }
.course-status.booked { background-color: #e6f3ff; color: #0052d9; }
.course-status.full { background-color: #fff2e8; color: #fa8c16; }
.course-status.ended { background-color: #f0f0f0; color: #888; }
.course-status.online { background-color: #e8f5e8; color: #52c41a; }
.course-status.offline { background-color: #fff2e8; color: #fa8c16; }
.course-status.no-status { background-color: #f0f0f0; color: #999; }
.course-status.template-status { background-color: #e6f3ff; color: #1890ff; }

/* ==================== 信息项目样式 ==================== */

.course-info-list, .template-info-list {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #666;
  gap: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item t-icon {
  color: #0052d9;
  flex-shrink: 0;
}

.info-item text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ==================== 卡片底部操作区域 ==================== */

.course-footer, .template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  gap: 16rpx;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 4rpx;
  min-width: 0;
}

.action-buttons .t-button {
  flex-shrink: 0;
  min-width: 140rpx;
  max-width: 200rpx;
}

.edit-disabled-btn {
  background-color: #f0f0f0 !important;
  border-color: #e0e0e0 !important;
  color: #bbb !important;
  cursor: not-allowed;
}

/* ==================== 已预约学员区域样式 ==================== */

.booked-students-section {
  margin: 24rpx 0;
  border: 1rpx solid #f0f0f0;
  border-radius: 16rpx;
  overflow: hidden;
}

.collapse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: #f8f8f8;
  cursor: pointer;
  font-size: 28rpx;
  color: #333;
}

.collapse-content {
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 0 32rpx;
}

.student-list {
  padding: 16rpx 0;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  font-size: 28rpx;
  color: #666;
  justify-content: space-between;
}

.student-item t-icon {
  margin-right: 16rpx;
  color: #0052d9;
}

.student-name {
  flex: 1;
  margin-right: 16rpx;
}

.remove-student-icon {
  flex-shrink: 0;
  margin-left: auto;
  padding: 8rpx;
  cursor: pointer;
  border-radius: 8rpx;
  transition: background-color 0.2s;
}

.remove-student-icon:hover {
  background-color: rgba(227, 77, 89, 0.1);
}

.no-students {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 40rpx 0;
}

/* ==================== 搜索和筛选样式 ==================== */

.search-actions-section {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-wrap: nowrap;
  justify-content: flex-start;
  box-sizing: border-box;
  overflow: visible;
  min-height: 64rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 16rpx;
  border: 2rpx solid #e7e7e7;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.04);
}

.search-actions-section.collapsed {
  justify-content: flex-start;
}

.collapsed-layout {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.search-actions-section.expanded {
  justify-content: flex-start;
}

.expanded-layout {
  flex: 1;
  height: 100%;
  display: flex;
  min-width: 0;
}

.search-icon-only {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64rpx;
  height: 64rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 2rpx solid #d9d9d9;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.search-icon-only:active {
  background: #f0f8ff;
  border-color: #0052d9;
  transform: scale(0.95);
}

.search-toggle-icon {
  color: #666666;
  font-size: 28rpx;
}

.search-input-container {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  border: 2rpx solid #d9d9d9;
  transition: all 0.2s ease;
  animation: searchExpand 0.3s ease-out;
  width: 100%;
  flex: 1;
  box-sizing: border-box;
  height: 64rpx;
}

.search-input-container:focus-within {
  border-color: #0052d9;
  box-shadow: 0 0 0 4rpx rgba(0, 82, 217, 0.1);
}

.search-icon {
  color: #999999;
  margin-right: 12rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  min-width: 0;
  height: 40rpx;
  line-height: 40rpx;
}

.search-input::placeholder {
  color: #999999;
  font-size: 28rpx;
}

.clear-icon,
.collapse-icon {
  color: #999999;
  margin-left: 12rpx;
  flex-shrink: 0;
  cursor: pointer;
  font-size: 28rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: all 0.2s ease;
}

.clear-icon:active,
.collapse-icon:active {
  color: #0052d9;
  background: rgba(0, 82, 217, 0.1);
  transform: scale(0.95);
}

@keyframes searchExpand {
  from {
    opacity: 0;
    transform: scaleX(0.8);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
  margin-left: auto;
  max-width: 70vw;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.actions-container::-webkit-scrollbar {
  display: none;
}

.actions-container .t-button {
  height: 64rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 140rpx;
  max-width: 200rpx;
}

/* ==================== 筛选菜单样式 ==================== */

.filter-dropdown-trigger {
  width: 240rpx;
  flex-shrink: 0;
  font-size: 28rpx;
}

.filter-button {
  height: 64rpx;
  padding: 0 16rpx 0 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8rpx;
  background-color: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  transition: all 0.2s ease;
  cursor: pointer;
  box-sizing: border-box;
}

.filter-button:hover {
  background-color: #f8f9fa;
  border-color: #0052d9;
}

.filter-button:active {
  background-color: #e6f4ff;
  transform: scale(0.98);
}

.filter-text {
  font-weight: 500;
  color: #333333;
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.filter-arrow {
  color: #999999;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.filter-arrow.rotated {
  transform: rotate(180deg);
}

.filter-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 400rpx;
}

.filter-menu {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 24rpx 96rpx rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-width: 480rpx;
  max-width: 640rpx;
  animation: filterMenuIn 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid rgba(0, 0, 0, 0.05);
}

@keyframes filterMenuIn {
  0% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.filter-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 48rpx 64rpx;
  font-size: 28rpx;
  color: #333333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 2rpx solid #f5f5f5;
}

.filter-menu-item:last-child {
  border-bottom: none;
}

.filter-menu-item:hover {
  background-color: #f8f9fa;
}

.filter-menu-item.active {
  background-color: #e6f4ff;
  color: #0052d9;
  font-weight: 500;
}

.filter-menu-item-text {
  flex: 1;
  font-size: 28rpx;
}

.filter-menu-item-check {
  color: #0052d9;
  margin-left: 32rpx;
}

/* ==================== 批量操作样式 ==================== */

.batch-actions-bar {
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  gap: 16rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9ff;
  border: 2rpx solid #e1f0ff;
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.1);
  animation: slide-down 0.3s ease-out;
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.batch-info {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
  max-width: 35%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.batch-buttons {
  gap: 12rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: flex-end;
  max-width: 65%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.batch-buttons .t-button {
  height: 64rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  min-width: 160rpx;
  max-width: 220rpx;
  border-radius: 12rpx;
  font-weight: 500;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.course-card.batch-mode, .template-card.batch-mode {
  border: 4rpx solid #0052d9;
  box-shadow: 0 8rpx 24rpx rgba(0, 82, 217, 0.2);
  transform: translateY(-4rpx);
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  overflow: hidden;
}

.course-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 6rpx;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.custom-checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.check-icon {
  color: #ffffff;
}

/* ==================== 加载状态和提示样式 ==================== */

.loading-indicator {
  text-align: center;
  color: #888;
  font-size: 24rpx;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.loading-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #0052d9;
  animation: loading-dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading-dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.end-indicator {
  text-align: center;
  color: #b0b0b0;
  font-size: 24rpx;
  padding: 32rpx 0;
  letter-spacing: 4rpx;
}

/* ==================== 对话框和浮动按钮样式 ==================== */

.remove-student-dialog-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.remove-student-dialog-content text {
  display: block;
  margin-bottom: 16rpx;
}

.remove-student-dialog-content .student-name {
  font-weight: bold;
  color: #333;
}

.refund-checkbox-container {
  display: flex;
  align-items: center;
  margin-top: 32rpx;
}

.refund-checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.2s ease;
}

.refund-checkbox.checked {
  background-color: #0052d9;
  border-color: #0052d9;
}

.refund-checkbox .check-icon {
  color: #ffffff;
}

.refund-label {
  font-size: 28rpx;
  color: #333;
}

.countdown-dialog-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.countdown-dialog-content text {
  display: block;
  margin-bottom: 16rpx;
}

.countdown-dialog-content .course-name {
  font-weight: bold;
  color: #d9534f;
}

.back-to-today-fab {
  position: fixed;
  right: 64rpx;
  bottom: 240rpx;
  z-index: 100;
}

.back-to-today-fab .t-fab__btn {
  background-color: rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

/* ==================== 响应式布局调整 ==================== */

/* 小屏幕优化 */
@media (max-width: 375px) {
  .container {
    padding: 24rpx;
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }

  .course-title, .template-title {
    font-size: 30rpx;
  }

  .info-item {
    font-size: 26rpx;
  }

  .actions-container .t-button,
  .batch-buttons .t-button {
    font-size: 24rpx;
    padding: 0 16rpx;
    min-width: 120rpx;
    max-width: 180rpx;
    height: 56rpx;
  }
}
